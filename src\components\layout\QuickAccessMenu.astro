---
import Icon from "../common/Icon.astro";
import LanguageSwitcher from "../common/LanguageSwitcher.astro";
import ThemeToggle from "../common/ThemeToggle.astro";

interface Props {
  githubUsername?: string;
  i18n: any;
  lang: "fr" | "en";
  initialOpen?: boolean;
  keycapLabel: string;
}

const {
  githubUsername = "Jamedie",
  lang = [
    { code: "fr", label: "Français" },
    { code: "en", label: "English" },
  ],
  initialOpen = false,
} = Astro.props as Props;

const { i18n, keycapLabel } = Astro.props;
---

<!--
QuickAccessMenu.astro
• Menu plein écran qui bloque le scroll
• Thème: clair/sombre/système (persisté)
• Langue: liste dynamique via props
• Activité GitHub publique (10 derniers events)
• Raccourcis en bas à gauche (Ctrl/⌘+K, T, L, Échap)


Utilisation:
<QuickAccessMenu githubUsername="tonUser" languages={[{code:'fr',label:'Français'},{code:'en',label:'English'}]} />


Styles: autonome via <style> ci-dessous (pas besoin de Tailwind/React)
-->
<script>
  const root = document.getElementById("quick-access-menu");
  const html = document.documentElement;

  function applyScrollLock() {
    if (!root) return;
    const isOpen = root.getAttribute("data-open") === "1";
    html.style.overflow = isOpen ? "hidden" : "";
  }

  applyScrollLock();

  if (root) {
    const obs = new MutationObserver(applyScrollLock);
    obs.observe(root, { attributes: true, attributeFilter: ["data-open"] });
  }
</script>

<section
  id="quick-access-menu"
  class="qa-root"
  data-open={initialOpen ? "1" : "0"}
  data-lang={lang || "fr"}
>
  <div class="qa-overlay"></div>
  <div
    class="qa-panel"
    role="dialog"
    aria-modal="true"
    aria-label="Quick Access"
    data-qa-panel
  >
    <div class="qa-tips-container">
      <div class="qa-tips-header">
        <Icon icon="keyboard" size="2rem" />
        <h4 class="no-select">{i18n.quickAccess.shortcuts.header}</h4>
      </div>

      <div class="qa-tip-desc">
        {i18n.quickAccess.shortcuts.description}
      </div>

      <div class="qa-tips-list">
        <div class="qa-tip">
          <p>{i18n.quickAccess.shortcuts.open}</p>
          <kbd>{keycapLabel}</kbd>
        </div>
        <div class="qa-separator"></div>
        <div class="qa-tip">
          <p>{i18n.quickAccess.shortcuts.close}</p>
          <p><kbd>{keycapLabel}</kbd> or <kbd>Esc</kbd></p>
        </div>
        <div class="qa-separator"></div>
        <div class="qa-tip">
          <p>{i18n.quickAccess.shortcuts.changeTheme}</p>
          <kbd>D</kbd>
        </div>
        <div class="qa-separator"></div>
        <div class="qa-tip">
          <p>{i18n.quickAccess.shortcuts.changeLanguage}</p>
          <kbd>L</kbd>
        </div>
        <div class="qa-separator"></div>
      </div>
    </div>
  </div>
</section>

<style>
  #quick-access-menu {
    position: fixed;
    inset: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;

    width: 100%;
    height: 100vh;
    background: var(--color-background-85a);
    backdrop-filter: blur(2px);

    pointer-events: none;
    transition: opacity 0.3s ease;
  }

  #quick-access-menu[data-open="1"] {
    opacity: 1;
  }

  #quick-access-menu[data-open="0"] {
    opacity: 0;
  }

  .qa-overlay {
    display: none !important;
  }

  .qa-panel {
    position: fixed;
    left: var(--spacing-15);
    bottom: var(--spacing-15);
    background: transparent;
    border: 0;
    box-shadow: none;
    padding: 0;

    z-index: 50;
    pointer-events: auto;
  }

  .qa-tips-container {
    display: flex;
    flex-direction: column;
    width: 320px;

    gap: var(--spacing-5);
  }

  .qa-tips-header {
    display: flex;
    align-items: center;
    gap: 0.4rem;
  }

  .qa-tip {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: var(--spacing-4);
    opacity: 0.9;
  }

  .qa-tip kbd {
    display: inline-block;
    min-width: 1.6em;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.1rem 0.4rem;
    font-size: 12px;
    background: transparent;
  }

  .qa-tip-desc {
    font-size: 0.9rem;
    color: var(--color-text-secondary);
  }

  .qa-separator {
    border-top: 1px solid var(--color-text-primary);
    margin: var(--spacing-2) 0;
  }
</style>
